#!/usr/bin/env node
"use strict";

function _interopRequireWildcard(obj) { if (obj && obj.__esModule) { return obj; } else { var newObj = {}; if (obj != null) { for (var key in obj) { if (Object.prototype.hasOwnProperty.call(obj, key)) newObj[key] = obj[key]; } } newObj["default"] = obj; return newObj; } }

var _path = require("path");

var _fs = require("fs");

var _distAcornJs = require("../dist/acorn.js");

var acorn = _interopRequireWildcard(_distAcornJs);

var infile = undefined,
    forceFile = undefined,
    silent = false,
    compact = false,
    tokenize = false;
var options = {};

function help(status) {
  var print = status == 0 ? console.log : console.error;
  print("usage: " + (0, _path.basename)(process.argv[1]) + " [--ecma3|--ecma5|--ecma6]");
  print("        [--tokenize] [--locations] [---allow-hash-bang] [--compact] [--silent] [--module] [--help] [--] [infile]");
  process.exit(status);
}

for (var i = 2; i < process.argv.length; ++i) {
  var arg = process.argv[i];
  if ((arg == "-" || arg[0] != "-") && !infile) infile = arg;else if (arg == "--" && !infile && i + 2 == process.argv.length) forceFile = infile = process.argv[++i];else if (arg == "--ecma3") options.ecmaVersion = 3;else if (arg == "--ecma5") options.ecmaVersion = 5;else if (arg == "--ecma6") options.ecmaVersion = 6;else if (arg == "--locations") options.locations = true;else if (arg == "--allow-hash-bang") options.allowHashBang = true;else if (arg == "--silent") silent = true;else if (arg == "--compact") compact = true;else if (arg == "--help") help(0);else if (arg == "--tokenize") tokenize = true;else if (arg == "--module") options.sourceType = 'module';else help(1);
}

function run(code) {
  var result = undefined;
  if (!tokenize) {
    try {
      result = acorn.parse(code, options);
    } catch (e) {
      console.error(e.message);process.exit(1);
    }
  } else {
    result = [];
    var tokenizer = acorn.tokenizer(code, options),
        token = undefined;
    while (true) {
      try {
        token = tokenizer.getToken();
      } catch (e) {
        console.error(e.message);process.exit(1);
      }
      result.push(token);
      if (token.type == acorn.tokTypes.eof) break;
    }
  }
  if (!silent) console.log(JSON.stringify(result, null, compact ? null : 2));
}

if (forceFile || infile && infile != "-") {
  run((0, _fs.readFileSync)(infile, "utf8"));
} else {
  (function () {
    var code = "";
    process.stdin.resume();
    process.stdin.on("data", function (chunk) {
      return code += chunk;
    });
    process.stdin.on("end", function () {
      return run(code);
    });
  })();
}