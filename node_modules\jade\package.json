{"name": "jade", "description": "A clean, whitespace-sensitive template language for writing HTML", "version": "1.11.0", "author": "<PERSON><PERSON> <<EMAIL>>", "maintainers": ["Forbes Lindesay <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <jona<PERSON><PERSON><PERSON><PERSON>@gmail.com>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <*************>"], "license": "MIT", "repository": {"type": "git", "url": "git://github.com/jadejs/jade"}, "main": "lib", "bin": {"jade": "./bin/jade.js"}, "dependencies": {"character-parser": "1.2.1", "clean-css": "^3.1.9", "commander": "~2.6.0", "constantinople": "~3.0.1", "jstransformer": "0.0.2", "mkdirp": "~0.5.0", "transformers": "2.1.0", "uglify-js": "^2.4.19", "void-elements": "~2.0.1", "with": "~4.0.0"}, "devDependencies": {"browserify": "*", "browserify-middleware": "~4.1.0", "code-mirror": "~3.22.0", "coffee-script": "*", "coveralls": "^2.11.2", "express": "~4.10.4", "github-basic": "^4.1.2", "handle": "~1.0.0", "highlight-codemirror": "~4.1.0", "inconsolata": "0.0.2", "istanbul": "*", "jade-code-mirror": "~1.0.5", "jade-highlighter": "~1.0.5", "jstransformer-cdata": "0.0.3", "jstransformer-coffee-script": "0.0.2", "jstransformer-less": "^1.0.0", "jstransformer-marked": "0.0.1", "jstransformer-stylus": "0.0.1", "jstransformer-verbatim": "0.0.2", "less": "<2.0.0", "less-file": "0.0.9", "linify": "*", "lsr": "^1.0.0", "marked": "~0.3.3", "mocha": "*", "opener": "^1.3.0", "pull-request": "^3.0.0", "rimraf": "^2.2.8", "should": "*", "stop": "^3.0.0-rc1", "stylus": "*", "twbs": "0.0.6", "uglify-js": "*"}, "component": {"scripts": {"jade": "runtime.js"}}, "scripts": {"test": "mocha -R spec", "precoverage": "rimraf coverage && rimraf cov-pt*", "coverage": "istanbul cover --report none --dir cov-pt0 node_modules/mocha/bin/_mocha -- -R dot", "postcoverage": "istanbul report --include cov-pt\\*/coverage.json && rimraf cov-pt*", "coveralls": "npm run coverage && cat ./coverage/lcov.info | coveralls", "prepublish": "npm prune && linify transform bin && npm run build", "build": "npm run compile", "compile": "npm run compile-full && npm run compile-runtime", "compile-full": "browserify ./lib/index.js --standalone jade -x ./node_modules/transformers > jade.js", "compile-runtime": "browserify ./lib/runtime.js --standalone jade > runtime.js"}, "browser": {"fs": false, "./lib/filters.js": "./lib/filters-client.js"}, "homepage": "http://jade-lang.com"}