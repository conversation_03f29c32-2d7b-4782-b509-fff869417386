{"name": "clean-css", "version": "3.4.28", "author": "<PERSON><PERSON><PERSON> <<EMAIL>> (http://twitter.com/jakubpawlowicz)", "description": "A well-tested CSS minifier", "license": "MIT", "keywords": ["css", "minifier"], "homepage": "https://github.com/jakubpawlowicz/clean-css", "repository": {"type": "git", "url": "https://github.com/jakubpawlowicz/clean-css.git"}, "bugs": {"url": "https://github.com/jakubpawlowicz/clean-css/issues"}, "bin": {"cleancss": "./bin/cleancss"}, "main": "index.js", "files": ["bin", "lib", "History.md", "index.js", "LICENSE"], "scripts": {"browserify": "browserify --standalone CleanCSS index.js | uglifyjs --compress --mangle -o cleancss-browser.js", "bench": "node ./test/bench.js", "check": "jshint ./bin/cleancss .", "prepublish": "npm run check", "test": "vows"}, "dependencies": {"commander": "2.8.x", "source-map": "0.4.x"}, "devDependencies": {"browserify": "11.x", "http-proxy": "1.x", "jshint": "2.8.x", "nock": "2.x", "server-destroy": "1.x", "uglify-js": "2.4.x", "vows": "0.8.x"}, "engines": {"node": ">=0.10.0"}}