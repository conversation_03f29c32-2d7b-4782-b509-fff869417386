{"name": "constantinople", "version": "3.0.2", "description": "Determine whether a JavaScript expression evaluates to a constant (using UglifyJS)", "keywords": [], "dependencies": {"acorn": "^2.1.0"}, "devDependencies": {"mocha": "*"}, "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "https://github.com/ForbesLindesay/constantinople.git"}, "author": "ForbesLindesay", "license": "MIT"}