{"name": "acorn", "description": "ECMAScript parser", "homepage": "https://github.com/ternjs/acorn", "main": "dist/acorn.js", "version": "2.7.0", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "web": "http://marijnhaverbeke.nl"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "web": "http://rreverser.com/"}], "repository": {"type": "git", "url": "https://github.com/ternjs/acorn.git"}, "license": "MIT", "scripts": {"prepublish": "node bin/build-acorn.js", "test": "node test/run.js"}, "bin": {"acorn": "./bin/acorn"}, "devDependencies": {"babel-core": "^5.6.15", "babelify": "^6.1.2", "browserify": "^10.2.4", "browserify-derequire": "^0.9.4", "unicode-7.0.0": "~0.1.5"}}