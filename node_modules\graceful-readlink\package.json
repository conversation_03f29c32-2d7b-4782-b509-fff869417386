{"name": "graceful-readlink", "version": "1.0.1", "description": "graceful fs.readlink", "main": "index.js", "repository": "git://github.com/zhiyelee/graceful-readlink.git", "homepage": "https://github.com/zhiyelee/graceful-readlink", "bugs": "https://github.com/zhiyelee/graceful-readlink/issues", "keywords": ["fs.readlink", "readlink"], "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}}